import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Product } from '../types';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { ArrowLeftIcon, ArrowRightIcon, ShoppingCartIcon, ShareIcon } from './common/Icon';
import VariantSelector from './VariantSelector';
import { getProductIdFromSlug, isValidProductSlug } from '../utils/urlUtils';

// Extended product data with additional details for product pages
const getProductDetails = (productId: string): Product & {
  images: string[];
  detailedDescription: string;
  specifications: { [key: string]: string };
  benefits: string[];
  usage: string[];
} | null => {
  const baseProducts: { [key: string]: any } = {
    'sp1': {
      id: 'sp1',
      name: 'Abaya Shampoo',
      material: 'Gentle Formula for Delicate Fabrics',
      price: 24.99,
      imageUrl: '/assets/products/abaya-shampoo.jpg',
      category: 'Fabric Care',
      features: ['Gentle', 'Fabric Safe'],
      images: [
        '/assets/products/abaya-shampoo.jpg',
        '/assets/products/abaya-shampoo.jpg',
        '/assets/products/abaya-shampoo.jpg'
      ],
      detailedDescription: 'Our premium Abaya Shampoo is specially formulated to provide gentle yet effective cleaning for delicate fabrics. This unique formula preserves the integrity and beauty of your precious garments while ensuring thorough cleansing.',
      specifications: {
        'Volume': '500ml',
        'pH Level': '6.5-7.0',
        'Fragrance': 'Light Floral',
        'Suitable For': 'Delicate Fabrics, Abayas, Silk',
        'Origin': 'UAE'
      },
      benefits: [
        'Gentle on delicate fabrics',
        'Preserves fabric integrity',
        'Light, pleasant fragrance',
        'Eco-friendly formula',
        'Concentrated formula for value'
      ],
      usage: [
        'Dilute 1 cap in 5 liters of water',
        'Soak garment for 5-10 minutes',
        'Gently hand wash or use delicate cycle',
        'Rinse thoroughly with clean water',
        'Air dry away from direct sunlight'
      ]
    },
    'sp2': {
      id: 'sp2',
      name: 'Premium Detergent Powder',
      material: 'Concentrated Cleaning Formula',
      price: 18.50,
      imageUrl: '/assets/products/detergent-powder.jpg',
      category: 'Laundry Care',
      features: ['Concentrated', 'Stain Removal'],
      images: [
        '/assets/products/detergent-powder.jpg',
        '/assets/products/detergent-powder.jpg',
        '/assets/products/detergent-powder.jpg'
      ],
      detailedDescription: 'Experience the power of our Premium Detergent Powder, engineered with advanced cleaning technology to tackle the toughest stains while being gentle on your clothes and the environment.',
      specifications: {
        'Weight': '1kg',
        'Loads': 'Up to 40 loads',
        'Temperature': 'Cold to Hot Water',
        'Fabric Types': 'All Fabrics',
        'Phosphate Free': 'Yes'
      },
      benefits: [
        'Superior stain removal',
        'Concentrated formula',
        'Color protection technology',
        'Fresh, long-lasting scent',
        'Biodegradable ingredients'
      ],
      usage: [
        'Use 1-2 scoops for regular loads',
        'Pre-treat stains for best results',
        'Add to washing machine before clothes',
        'Suitable for all water temperatures',
        'Store in cool, dry place'
      ]
    },
    'sp3': {
      id: 'sp3',
      name: 'Dishwash Liquid',
      material: 'Grease-Cutting Formula',
      price: 12.75,
      imageUrl: '/assets/products/dishwash-liquid.jpg',
      category: 'Kitchen Care',
      features: ['Grease-Cutting', 'Gentle on Hands'],
      images: [
        '/assets/products/dishwash-liquid.jpg',
        '/assets/products/dishwash-liquid.jpg',
        '/assets/products/dishwash-liquid.jpg'
      ],
      detailedDescription: 'Our advanced Dishwash Liquid combines powerful grease-cutting action with gentle care for your hands. Perfect for all your dishwashing needs, from everyday plates to the greasiest pans.',
      specifications: {
        'Volume': '750ml',
        'pH Level': '7.0-8.0',
        'Fragrance': 'Fresh Lemon',
        'Antibacterial': 'Yes',
        'Dermatologically Tested': 'Yes'
      },
      benefits: [
        'Cuts through grease instantly',
        'Gentle on hands',
        'Antibacterial protection',
        'Pleasant lemon fragrance',
        'Concentrated formula'
      ],
      usage: [
        'Apply small amount to sponge or directly to dishes',
        'Work into a rich lather',
        'Rinse thoroughly with water',
        'For tough grease, let sit for 2-3 minutes',
        'Safe for all dishware materials'
      ]
    },
    'sp4': {
      id: 'sp4',
      name: 'Professional Emulsifiers',
      material: 'Industrial Grade Cleaning Agent',
      price: 32.00,
      imageUrl: '/assets/products/emulsifiers.jpg',
      category: 'Professional Care',
      features: ['Industrial Grade', 'Multi-Purpose'],
      images: [
        '/assets/products/emulsifiers.jpg',
        '/assets/products/emulsifiers.jpg',
        '/assets/products/emulsifiers.jpg'
      ],
      detailedDescription: 'Our Professional Emulsifiers are designed for industrial-grade cleaning applications. These powerful agents break down the toughest grease and grime, making them perfect for commercial and heavy-duty cleaning tasks.',
      specifications: {
        'Volume': '1L',
        'Concentration': 'High',
        'Application': 'Industrial/Commercial',
        'pH Level': '8.5-9.0',
        'Biodegradable': 'Yes'
      },
      benefits: [
        'Industrial-grade cleaning power',
        'Breaks down tough grease and grime',
        'Multi-purpose application',
        'Cost-effective concentrated formula',
        'Professional quality results'
      ],
      usage: [
        'Dilute according to cleaning task requirements',
        'Apply to surface and allow to penetrate',
        'Agitate with brush or cloth if needed',
        'Rinse thoroughly with clean water',
        'Use appropriate safety equipment'
      ]
    },
    'sp5': {
      id: 'sp5',
      name: 'Fabric Softener',
      material: 'Ultra-Soft Conditioning Formula',
      price: 16.25,
      imageUrl: '/assets/products/fabric-softner.jpg',
      category: 'Laundry Care',
      features: ['Ultra-Soft', 'Fresh Scent'],
      images: [
        '/assets/products/fabric-softner.jpg',
        '/assets/products/fabric-softner.jpg',
        '/assets/products/fabric-softner.jpg'
      ],
      detailedDescription: 'Transform your laundry with our Ultra-Soft Fabric Softener. This premium conditioning formula leaves your clothes incredibly soft, reduces static, and infuses them with a long-lasting fresh scent.',
      specifications: {
        'Volume': '750ml',
        'Loads': 'Up to 30 loads',
        'Fragrance': 'Fresh Breeze',
        'Anti-Static': 'Yes',
        'Hypoallergenic': 'Yes'
      },
      benefits: [
        'Ultra-soft fabric conditioning',
        'Reduces static cling',
        'Long-lasting fresh fragrance',
        'Gentle on sensitive skin',
        'Works in all water temperatures'
      ],
      usage: [
        'Add to fabric softener dispenser',
        'Use 1 cap for regular loads',
        'For hand washing, add during final rinse',
        'Do not pour directly on clothes',
        'Store in cool, dry place'
      ]
    },
    'sp6': {
      id: 'sp6',
      name: 'Gentle Hand Wash',
      material: 'Moisturizing Hand Care Formula',
      price: 14.99,
      imageUrl: '/assets/products/handwash-1.jpg',
      category: 'Personal Care',
      features: ['Moisturizing', 'Antibacterial'],
      variants: [
        {
          id: 'green-apple',
          name: 'Green Apple',
          imageUrl: '/assets/products/handwash-1.jpg',
          color: { name: 'Green', hex: '#22C55E' }
        },
        {
          id: 'strawberry',
          name: 'Strawberry',
          imageUrl: '/assets/products/handwash-2.jpg',
          color: { name: 'Red', hex: '#EF4444' }
        },
        {
          id: 'lavender',
          name: 'Lavender',
          imageUrl: '/assets/products/handwash-3.jpg',
          color: { name: 'Purple', hex: '#A855F7' }
        }
      ],
      selectedVariant: 'green-apple',
      availableColorCount: 3,
      images: [
        '/assets/products/handwash-1.jpg',
        '/assets/products/handwash-2.jpg',
        '/assets/products/handwash-3.jpg'
      ],
      detailedDescription: 'Our Gentle Hand Wash combines effective cleansing with moisturizing care. Available in three delightful fragrances - Green Apple, Strawberry, and Lavender - this antibacterial formula keeps your hands clean, soft, and beautifully scented. Each variant offers a unique sensory experience while providing the same gentle yet effective cleansing power.',
      specifications: {
        'Volume': '300ml',
        'pH Level': '6.0-7.0',
        'Antibacterial': 'Yes',
        'Moisturizing Agents': 'Aloe Vera, Glycerin',
        'Available Flavors': 'Green Apple, Strawberry, Lavender',
        'Variants': '3 Different Fragrances'
      },
      benefits: [
        'Gentle antibacterial protection',
        'Moisturizes while cleansing',
        'Available in 3 beautiful fragrances',
        'Suitable for frequent use',
        'Dermatologically tested'
      ],
      usage: [
        'Apply small amount to wet hands',
        'Rub hands together for 20 seconds',
        'Rinse thoroughly with water',
        'Use as often as needed',
        'Suitable for all skin types'
      ]
    },
    'sp7': {
      id: 'sp7',
      name: 'Laundry Detergent',
      material: 'Advanced Cleaning Technology',
      price: 22.50,
      imageUrl: '/assets/products/laundry-detergent.jpg',
      category: 'Laundry Care',
      features: ['Advanced Formula', 'Color Protection'],
      images: [
        '/assets/products/laundry-detergent.jpg',
        '/assets/products/laundry-detergent.jpg',
        '/assets/products/laundry-detergent.jpg'
      ],
      detailedDescription: 'Experience superior cleaning with our Advanced Laundry Detergent. This innovative formula combines powerful stain removal with color protection technology to keep your clothes looking vibrant and fresh.',
      specifications: {
        'Volume': '1L',
        'Loads': 'Up to 50 loads',
        'Formula': 'Concentrated Liquid',
        'Color Protection': 'Yes',
        'Enzyme Action': 'Multi-enzyme blend'
      },
      benefits: [
        'Advanced stain removal technology',
        'Protects fabric colors',
        'Works in all water temperatures',
        'Concentrated formula for value',
        'Fresh, clean scent'
      ],
      usage: [
        'Use 1-2 caps depending on load size',
        'Add to washing machine before clothes',
        'For tough stains, pre-treat directly',
        'Suitable for all fabric types',
        'Works in both top and front loaders'
      ]
    },
    'sp8': {
      id: 'sp8',
      name: 'Taze Fresh Cleaner',
      material: 'All-Purpose Cleaning Solution',
      price: 19.75,
      imageUrl: '/assets/products/taze.jpg',
      category: 'All-Purpose',
      features: ['Fresh Scent', 'Multi-Surface'],
      images: [
        '/assets/products/taze.jpg',
        '/assets/products/taze.jpg',
        '/assets/products/taze.jpg'
      ],
      detailedDescription: 'Taze Fresh Cleaner is your go-to solution for all-purpose cleaning. This versatile cleaner works on multiple surfaces, leaving behind a refreshing scent and spotless results every time.',
      specifications: {
        'Volume': '500ml',
        'Surface Types': 'Multi-surface',
        'Fragrance': 'Fresh Citrus',
        'Disinfectant': 'Yes',
        'Streak-Free': 'Yes'
      },
      benefits: [
        'Cleans multiple surface types',
        'Refreshing citrus fragrance',
        'Streak-free finish',
        'Disinfects while cleaning',
        'Easy-to-use spray bottle'
      ],
      usage: [
        'Spray directly onto surface',
        'Wipe with clean cloth or paper towel',
        'For tough stains, let sit for 30 seconds',
        'No rinsing required on most surfaces',
        'Test on inconspicuous area first'
      ]
    },
    'sp9': {
      id: 'sp9',
      name: 'Toilet Cleaner',
      material: 'Powerful Disinfectant Formula',
      price: 13.25,
      imageUrl: '/assets/products/toilet-cleaner.jpg',
      category: 'Bathroom Care',
      features: ['Disinfectant', 'Deep Clean'],
      images: [
        '/assets/products/toilet-cleaner.jpg',
        '/assets/products/toilet-cleaner.jpg',
        '/assets/products/toilet-cleaner.jpg'
      ],
      detailedDescription: 'Our Powerful Toilet Cleaner provides deep cleaning and disinfection for your bathroom. This specialized formula eliminates germs, removes stains, and leaves your toilet sparkling clean with a fresh scent.',
      specifications: {
        'Volume': '750ml',
        'Disinfectant': '99.9% germ kill',
        'Stain Removal': 'Lime scale & rust',
        'Fragrance': 'Fresh Clean',
        'Toilet Bowl Safe': 'Yes'
      },
      benefits: [
        'Kills 99.9% of germs and bacteria',
        'Removes tough stains and lime scale',
        'Deep cleaning action',
        'Fresh, clean fragrance',
        'Safe for all toilet types'
      ],
      usage: [
        'Apply under toilet rim and bowl',
        'Allow to sit for 10 minutes',
        'Scrub with toilet brush',
        'Flush to rinse',
        'Use regularly for best results'
      ]
    }
  };

  return baseProducts[productId] || null;
};

const ProductDetailPage: React.FC = () => {
  const { productSlug } = useParams<{ productSlug: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addToCart } = useCart();
  const [product, setProduct] = useState<any>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set());
  const [selectedVariantId, setSelectedVariantId] = useState<string>('');

  const sectionRef = useRef<HTMLDivElement>(null);
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  useEffect(() => {
    if (!productSlug) return;

    // Convert slug to product ID
    let productId: string;

    // Check if it's a valid slug
    if (isValidProductSlug(productSlug)) {
      productId = getProductIdFromSlug(productSlug);
    } else {
      // Fallback: treat as product ID for backward compatibility
      productId = productSlug;
    }

    const productData = getProductDetails(productId);

    // If product not found, redirect to store
    if (!productData) {
      navigate('/store');
      return;
    }

    setProduct(productData);

    // Update page title for SEO
    document.title = `${productData.name} - Le Prestine | Premium Cleaning Products`;

    // Initialize selected variant
    if (productData?.variants && productData.variants.length > 0) {
      setSelectedVariantId(productData.selectedVariant || productData.variants[0].id);
    }
  }, [productSlug, navigate]);

  // Cleanup: Reset page title when component unmounts
  useEffect(() => {
    return () => {
      document.title = 'Le Prestine | Premium Cleaning Products';
    };
  }, []);

  // Get current variant data
  const getCurrentVariant = () => {
    if (!product?.variants) return null;
    return product.variants.find((v: any) => v.id === selectedVariantId) || product.variants[0];
  };

  // Handle variant selection
  const handleVariantSelect = (variantId: string) => {
    setSelectedVariantId(variantId);
    setCurrentImageIndex(0); // Reset to first image when variant changes
  };

  // Get current images array (variant-specific or default)
  const getCurrentImages = () => {
    if (product?.variants && product.variants.length > 0) {
      // For products with variants, show only the selected variant image
      const currentVariant = getCurrentVariant();
      return currentVariant && currentVariant.imageUrl ? [currentVariant.imageUrl] : [product.imageUrl];
    }
    // For products without variants, use the original images array
    return product?.images || (product?.imageUrl ? [product.imageUrl] : []);
  };

  // Get current product image (variant-specific or default)
  const getCurrentProductImage = () => {
    const images = getCurrentImages();
    return images[currentImageIndex] || images[0];
  };

  // Navigation functions for image carousel
  const nextImage = () => {
    const images = getCurrentImages();
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    const images = getCurrentImages();
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    setIsAddingToCart(true);
    const result = await addToCart(product.id, 1);
    setIsAddingToCart(false);

    if (result.success) {
      // Show success feedback (you could add a toast notification here)
      console.log('Added to cart successfully');
    } else {
      // Show error feedback
      console.error('Failed to add to cart:', result.error);
      alert(result.error || 'Failed to add item to cart');
    }
  };



  // Intersection Observer for individual sections
  useEffect(() => {
    const observers: IntersectionObserver[] = [];
    
    Object.entries(sectionRefs.current).forEach(([key, element]) => {
      if (element) {
        const observer = new IntersectionObserver(
          ([entry]) => {
            if (entry.isIntersecting) {
              setVisibleSections(prev => new Set([...prev, key]));
            }
          },
          { threshold: 0.3 }
        );
        
        observer.observe(element);
        observers.push(observer);
      }
    });

    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, [product]);

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }



  return (
    <div ref={sectionRef} className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      <div className="relative z-10">
        {/* Breadcrumb Navigation */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-8">
          <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
            <button
              onClick={() => navigate('/')}
              className="hover:text-brand-accent-teal transition-colors duration-300"
            >
              Home
            </button>
            <span>/</span>
            <button
              onClick={() => navigate('/store')}
              className="hover:text-brand-accent-teal transition-colors duration-300"
            >
              Store
            </button>
            <span>/</span>
            <span className="text-brand-accent-teal font-medium">{product.name}</span>
          </nav>

          {/* Go Back Navigation */}
          <div className="mb-8">
            <button
              onClick={() => navigate('/store')}
              className="group flex items-center space-x-2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-700 hover:text-brand-accent-teal px-6 py-3 rounded-xl border border-gray-200/50 hover:border-brand-accent-teal/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-medium"
            >
              <ArrowLeftIcon size={20} className="transform group-hover:-translate-x-1 transition-transform duration-300" />
              <span>Go Back</span>
            </button>
          </div>
        </div>

        {/* Main Product Section */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 mb-16">
            {/* Product Images */}
            <div
              ref={(el) => { sectionRefs.current['images'] = el; }}
              className={`transition-all duration-700 ${
                visibleSections.has('images') ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'
              }`}
            >
              {/* Main Image */}
              <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-2xl border border-white/50 mb-6 group overflow-hidden">
                {/* Glass Morphism Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>
                
                <div className="relative aspect-square overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100">
                  <img
                    src={getCurrentProductImage() || '/assets/products/handwash-1.jpg'}
                    alt={`${product.name}${getCurrentVariant() ? ` - ${getCurrentVariant().name}` : ''}`}
                    className="w-full h-full object-cover transition-all duration-500 group-hover:scale-105"
                    onError={(e) => {
                      e.currentTarget.src = '/assets/products/handwash-1.jpg';
                    }}
                  />
                  
                  {/* Navigation Arrows */}
                  {getCurrentImages().length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white/95 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <ArrowLeftIcon size={20} className="text-gray-700" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white/95 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <ArrowRightIcon size={20} className="text-gray-700" />
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Thumbnail Images */}
              {getCurrentImages().length > 1 && (
                <div className="flex space-x-4 justify-center">
                  {getCurrentImages().map((image: string, index: number) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`relative w-20 h-20 rounded-xl overflow-hidden transition-all duration-300 ${
                        index === currentImageIndex
                          ? 'ring-2 ring-brand-accent-teal scale-110'
                          : 'hover:scale-105 opacity-70 hover:opacity-100'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${product.name} view ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information */}
            <div
              ref={(el) => { sectionRefs.current['info'] = el; }}
              className={`transition-all duration-700 ${
                visibleSections.has('info') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '200ms' }}
            >
              {/* Product Header */}
              <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50 mb-6">
                {/* Glass Morphism Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

                <div className="relative">
                  {/* Category Badge */}
                  <span className="inline-block bg-brand-accent-teal/10 text-brand-accent-teal px-4 py-2 rounded-full text-sm font-medium mb-4">
                    {product.category}
                  </span>

                  {/* Product Name */}
                  <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 leading-tight">
                    {product.name}
                    {getCurrentVariant() && (
                      <span className="block text-2xl lg:text-3xl text-brand-accent-teal mt-2">
                        {getCurrentVariant().name} Flavor
                      </span>
                    )}
                  </h1>

                  {/* Material/Subtitle */}
                  <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                    {product.material}
                  </p>

                  {/* Price with discount information */}
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex flex-col">
                      {/* Discount badge and original price */}
                      {product.original_price && product.discount && (
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="bg-brand-main-red text-white text-sm px-3 py-1 rounded-full font-bold">
                            {product.discount}
                          </span>
                          <span className="text-lg text-gray-500 line-through">
                            AED {product.original_price.toFixed(2)}
                          </span>
                        </div>
                      )}

                      {/* Current price */}
                      <div className="text-3xl font-bold bg-gradient-to-r from-brand-accent-teal to-brand-main-red bg-clip-text text-transparent">
                        AED {product.price.toFixed(2)}
                      </div>

                      {/* Savings amount */}
                      {product.original_price && (
                        <span className="text-lg text-brand-accent-teal font-semibold mt-1">
                          You save AED {(product.original_price - product.price).toFixed(2)}
                        </span>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button className="p-3 bg-gray-100 hover:bg-gray-200 rounded-full transition-all duration-300 hover:scale-110">
                        <ShareIcon size={20} className="text-gray-600" />
                      </button>
                    </div>
                  </div>

                  {/* Variant Selector */}
                  {product.variants && product.variants.length > 0 && (
                    <div className="mb-8">
                      <VariantSelector
                        variants={product.variants}
                        selectedVariantId={selectedVariantId}
                        onVariantSelect={handleVariantSelect}
                      />
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-4">
                    {/* Buy Now Button */}
                    <button className="flex-1 bg-gradient-to-r from-brand-accent-teal to-brand-main-red text-white py-4 px-8 rounded-2xl font-semibold text-lg hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-3 group">
                      <span>Buy Now</span>
                    </button>

                    {/* Add to Cart Button */}
                    <button
                      onClick={handleAddToCart}
                      disabled={isAddingToCart}
                      className="bg-white border-2 border-brand-accent-teal text-brand-accent-teal hover:bg-brand-accent-teal hover:text-white py-4 px-6 rounded-2xl font-semibold text-lg hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                    >
                      {isAddingToCart ? (
                        <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <ShoppingCartIcon size={24} className="group-hover:scale-110 transition-transform duration-300" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

                  <div className="relative">
                    <h3 className="text-xl font-bold text-gray-900 mb-4">Key Features</h3>
                    <div className="flex flex-wrap gap-3">
                      {product.features.map((feature: string, index: number) => (
                        <span
                          key={index}
                          className="bg-brand-accent-teal/10 text-brand-accent-teal px-4 py-2 rounded-full text-sm font-medium"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Detailed Information Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Description */}
            <div
              ref={(el) => { sectionRefs.current['description'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('description') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '300ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Product Description</h3>
                <p className="text-gray-700 leading-relaxed text-lg">
                  {product.detailedDescription}
                </p>
              </div>
            </div>

            {/* Specifications */}
            <div
              ref={(el) => { sectionRefs.current['specifications'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('specifications') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '400ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Specifications</h3>
                <div className="space-y-4">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between items-center py-3 border-b border-gray-200/50 last:border-b-0">
                      <span className="font-medium text-gray-700">{key}:</span>
                      <span className="text-gray-900 font-semibold">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Benefits and Usage */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Benefits */}
            <div
              ref={(el) => { sectionRefs.current['benefits'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('benefits') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '500ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Benefits</h3>
                <ul className="space-y-3">
                  {product.benefits.map((benefit: string, index: number) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-brand-accent-teal rounded-full mt-3 flex-shrink-0"></div>
                      <span className="text-gray-700 leading-relaxed">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Usage Instructions */}
            <div
              ref={(el) => { sectionRefs.current['usage'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('usage') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '600ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">How to Use</h3>
                <ol className="space-y-3">
                  {product.usage.map((step: string, index: number) => (
                    <li key={index} className="flex items-start space-x-4">
                      <div className="w-8 h-8 bg-brand-accent-teal text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-gray-700 leading-relaxed pt-1">{step}</span>
                    </li>
                  ))}
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
