// Simple script to check database tables and data
// Run this in browser console to debug database issues

import { supabase } from './lib/supabase.js';

// Check if products table exists and has data
async function checkDatabase() {
  console.log('Checking database...');
  
  try {
    // Check products table
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (productsError) {
      console.error('Products table error:', productsError);
    } else {
      console.log('Products found:', products?.length || 0);
      console.log('Sample products:', products);
    }
    
    // Check cart_items table structure
    const { data: cartItems, error: cartError } = await supabase
      .from('cart_items')
      .select('*')
      .limit(1);
    
    if (cartError) {
      console.error('Cart items table error:', cartError);
    } else {
      console.log('Cart items table accessible');
    }
    
    // Check current user
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Current user:', user?.id);
    
  } catch (error) {
    console.error('Database check failed:', error);
  }
}

// Export for use in console
window.checkDatabase = checkDatabase;
console.log('Run checkDatabase() in console to debug database');
