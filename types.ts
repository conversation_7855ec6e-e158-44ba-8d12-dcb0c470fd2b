
export interface ProductColor {
  name: string;
  hex: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  imageUrl: string;
  color?: ProductColor;
}

export interface Product {
  id: string;
  name: string;
  category?: string;
  price: number;
  originalPrice?: number;
  original_price?: number; // Database field name
  discount?: string;
  imageUrl: string;
  image_url?: string; // Database field name
  description?: string;
  material?: string;
  colors?: ProductColor[];
  availableColorCount?: number;
  features?: string[];
  variants?: ProductVariant[];
  selectedVariant?: string; // ID of currently selected variant
}

export interface CategoryInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

// Cart-related types
export interface CartItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  product?: Product; // Populated when fetching cart with product details
}

export interface CartSummary {
  subtotal: number;
  tax: number;
  total: number;
  itemCount: number;
  totalSavings?: number;
  originalTotal?: number;
}